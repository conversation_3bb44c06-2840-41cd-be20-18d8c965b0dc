import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  Index,
} from 'typeorm';
import { Exclude } from 'class-transformer';
import { MT5Account } from '../../accounts/entities/mt5-account.entity';

export enum UserRole {
  USER = 'user',
  ADMIN = 'admin',
  SUPER_ADMIN = 'super_admin',
}

export enum SubscriptionType {
  FREE = 'free',
  BASIC = 'basic',
  PREMIUM = 'premium',
  ENTERPRISE = 'enterprise',
}

@Entity('users')
@Index(['email'])
@Index(['subscription_type', 'subscription_expires_at'])
@Index(['is_active'])
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true, length: 255 })
  email: string;

  @Column({ length: 255 })
  @Exclude()
  password_hash: string;

  @Column({ length: 100 })
  first_name: string;

  @Column({ length: 100 })
  last_name: string;

  @Column({
    type: 'enum',
    enum: UserRole,
    default: UserRole.USER,
  })
  role: UserRole;

  @Column({
    type: 'enum',
    enum: SubscriptionType,
    default: SubscriptionType.FREE,
  })
  subscription_type: SubscriptionType;

  @Column({ type: 'timestamptz', nullable: true })
  subscription_expires_at: Date;

  @Column({ default: false })
  email_verified: boolean;

  @Column({ default: true })
  is_active: boolean;

  @Column({ type: 'timestamptz', nullable: true })
  last_login_at: Date;

  @CreateDateColumn({ type: 'timestamptz' })
  created_at: Date;

  @UpdateDateColumn({ type: 'timestamptz' })
  updated_at: Date;

  // Relations
  @OneToMany(() => MT5Account, (account) => account.user)
  mt5_accounts: MT5Account[];

  // Virtual properties
  get full_name(): string {
    return `${this.first_name} ${this.last_name}`;
  }

  get is_subscription_active(): boolean {
    if (!this.subscription_expires_at) return true;
    return new Date() < this.subscription_expires_at;
  }
}