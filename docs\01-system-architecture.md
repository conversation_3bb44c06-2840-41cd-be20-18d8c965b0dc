# MT5 Copy Trading System - System Architecture

## Overview
This document outlines the comprehensive system architecture for a scalable MT5 copy trading system capable of handling several thousand accounts with real-time signal distribution.

## High-Level Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   EA MASTER     │    │   BACKEND API   │    │   EA SLAVE      │
│   (Signal Gen)  │───▶│   (NestJS)      │───▶│   (Signal Exec) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │  WEB DASHBOARD  │
                       │   (NextJS)      │
                       └─────────────────┘
```

## Detailed Data Flow Architecture

### 1. Signal Generation Flow (EA MASTER → Backend)
```
EA MASTER (MT5) → HTTP POST /api/signals → Backend API → Redis Pub/Sub → Database
```

### 2. Signal Distribution Flow (Backend → EA SLAVE)
```
Backend API → Redis Cache → HTTP GET /api/signals/pending → EA SLAVE (MT5)
```

### 3. Real-time Dashboard Updates
```
Backend API → WebSocket Server → Frontend Dashboard
```

## Communication Protocols Recommendation

### Primary Protocol: HTTP REST + Redis Pub/Sub
**Rationale**: Optimal balance of reliability, scalability, and MT5 compatibility

1. **EA MASTER → Backend**: HTTP POST requests
   - Reliable delivery with retry mechanisms
   - Simple implementation in MQL5
   - Stateless and scalable

2. **Backend → EA SLAVE**: HTTP GET polling + Redis caching
   - Polling frequency: 1-2 seconds for active signals
   - Redis cache reduces database load
   - Fallback to database if Redis unavailable

3. **Backend → Dashboard**: WebSockets
   - Real-time updates for web interface
   - Server-sent events as fallback

### Alternative Protocols Considered

| Protocol | Pros | Cons | Recommendation |
|----------|------|------|----------------|
| WebSockets | Real-time, bidirectional | Complex in MQL5, connection management | Dashboard only |
| Server-Sent Events | Simple, auto-reconnect | One-way communication | Dashboard fallback |
| Long Polling | Real-time feel, HTTP-based | Resource intensive | Not recommended |

## Authentication & Authorization System

### JWT-Based Authentication
```
┌─────────────────┐
│   API Gateway   │
│  (Rate Limiting)│
└─────────────────┘
         │
         ▼
┌─────────────────┐    ┌─────────────────┐
│  Auth Service   │───▶│   JWT Tokens    │
│  (NestJS)       │    │  (Access/Refresh)│
└─────────────────┘    └─────────────────┘
```

### Security Layers
1. **API Key Authentication** for EA communication
2. **JWT Tokens** for web dashboard
3. **Rate Limiting** per account/IP
4. **Request Signing** for critical operations

## Performance Requirements

### Latency Targets
- **Signal Generation to Distribution**: < 500ms (P95)
- **Signal Reception to Execution**: < 200ms (P95)
- **End-to-End Latency**: < 1 second (P95)

### Throughput Requirements
- **Concurrent Users**: 10,000+ accounts
- **Signals per Second**: 1,000+ during peak hours
- **API Requests per Second**: 50,000+ (including polling)

### Availability Requirements
- **Uptime**: 99.9% (8.76 hours downtime/year)
- **Recovery Time**: < 5 minutes
- **Data Consistency**: Eventual consistency acceptable for non-critical data

## Technology Stack

### Backend Infrastructure
- **Application**: NestJS (Node.js)
- **Database**: PostgreSQL 14+ (Primary)
- **Cache**: Redis 7+ (Session, Signals)
- **Message Queue**: Redis Pub/Sub
- **Load Balancer**: Nginx/HAProxy
- **Container**: Docker + Kubernetes

### Frontend
- **Framework**: NextJS 14+ (React)
- **State Management**: Zustand/Redux Toolkit
- **Real-time**: Socket.io client
- **UI Library**: Tailwind CSS + shadcn/ui

### Monitoring & Observability
- **Metrics**: Prometheus + Grafana
- **Logging**: ELK Stack (Elasticsearch, Logstash, Kibana)
- **Tracing**: Jaeger
- **Alerting**: AlertManager + PagerDuty

## Scalability Design

### Horizontal Scaling Strategy
```
┌─────────────────┐
│  Load Balancer  │
│   (Nginx/HAProxy)│
└─────────────────┘
         │
    ┌────┴────┐
    ▼         ▼
┌─────────┐ ┌─────────┐
│Backend-1│ │Backend-2│
└─────────┘ └─────────┘
    │         │
    └────┬────┘
         ▼
┌─────────────────┐
│  Redis Cluster  │
│  (Pub/Sub + Cache)│
└─────────────────┘
         │
         ▼
┌─────────────────┐
│ PostgreSQL HA   │
│ (Master/Replica)│
└─────────────────┘
```

### Auto-scaling Triggers
- CPU utilization > 70%
- Memory utilization > 80%
- API response time > 1 second
- Queue depth > 1000 messages

## Network Architecture

### Production Deployment
```
Internet → CDN → Load Balancer → API Gateway → Backend Services
                                      │
                                      ▼
                              Private Network
                              (Database, Redis, Internal Services)
```

### Security Zones
1. **DMZ**: Load balancers, API gateways
2. **Application Tier**: Backend services
3. **Data Tier**: Databases, caches (private network only)

## Disaster Recovery

### Backup Strategy
- **Database**: Continuous WAL archiving + daily snapshots
- **Redis**: RDB snapshots every 15 minutes
- **Application**: Stateless containers (no backup needed)

### Recovery Procedures
- **RTO (Recovery Time Objective)**: 15 minutes
- **RPO (Recovery Point Objective)**: 5 minutes
- **Multi-region deployment** for critical components