# Application Configuration
NODE_ENV=development
PORT=3000
API_PREFIX=api
CORS_ORIGIN=http://localhost:3001

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=copytrading
DB_PASSWORD=your_secure_password
DB_NAME=mt5_copytrading
DB_SYNCHRONIZE=false
DB_LOGGING=true

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
REDIS_DB=0

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here_change_in_production
JWT_EXPIRES_IN=24h
JWT_REFRESH_SECRET=your_super_secret_refresh_key_here
JWT_REFRESH_EXPIRES_IN=7d

# Encryption Configuration
ENCRYPTION_KEY=your_32_character_encryption_key_here

# Rate Limiting
RATE_LIMIT_TTL=60
RATE_LIMIT_LIMIT=100
SIGNAL_RATE_LIMIT=200

# Monitoring & Logging
LOG_LEVEL=info
ENABLE_SWAGGER=true
ENABLE_METRICS=true
ENABLE_ALERTS=false

# External Services
WEBHOOK_URL=
NOTIFICATION_SERVICE_URL=
MONITORING_SERVICE_URL=

# Security
API_KEY_LENGTH=32
SESSION_SECRET=your_session_secret_here
BCRYPT_ROUNDS=12

# Performance
CACHE_TTL=300
SIGNAL_CACHE_TTL=30
ACCOUNT_CACHE_TTL=60