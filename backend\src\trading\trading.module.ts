import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TradingService } from './trading.service';
import { TradingController } from './trading.controller';
import { TradeExecution } from './entities/trade-execution.entity';

@Module({
  imports: [TypeOrmModule.forFeature([TradeExecution])],
  controllers: [TradingController],
  providers: [TradingService],
  exports: [TradingService],
})
export class TradingModule {}