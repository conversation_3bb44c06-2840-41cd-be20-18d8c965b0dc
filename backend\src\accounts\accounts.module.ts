import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AccountsService } from './accounts.service';
import { AccountsController } from './accounts.controller';
import { MT5Account } from './entities/mt5-account.entity';
import { AccountPerformance } from './entities/account-performance.entity';

@Module({
  imports: [TypeOrmModule.forFeature([MT5Account, AccountPerformance])],
  controllers: [AccountsController],
  providers: [AccountsService],
  exports: [AccountsService],
})
export class AccountsModule {}