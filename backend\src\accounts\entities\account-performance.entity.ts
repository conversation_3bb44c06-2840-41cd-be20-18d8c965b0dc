import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  Index,
  Unique,
} from 'typeorm';
import { MT5Account } from './mt5-account.entity';

@Entity('account_performance')
@Unique(['account_id', 'date'])
@Index(['account_id', 'date'])
@Index(['date'])
export class AccountPerformance {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  account_id: string;

  @Column({ type: 'date' })
  date: Date;

  @Column({ type: 'decimal', precision: 15, scale: 2 })
  starting_balance: number;

  @Column({ type: 'decimal', precision: 15, scale: 2 })
  ending_balance: number;

  @Column({ type: 'decimal', precision: 15, scale: 2 })
  starting_equity: number;

  @Column({ type: 'decimal', precision: 15, scale: 2 })
  ending_equity: number;

  @Column({ type: 'decimal', precision: 15, scale: 2 })
  daily_pnl: number;

  @Column({ type: 'int', default: 0 })
  total_trades: number;

  @Column({ type: 'int', default: 0 })
  winning_trades: number;

  @Column({ type: 'int', default: 0 })
  losing_trades: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  total_volume: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  max_drawdown: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  commission_paid: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  swap_paid: number;

  @Column({ type: 'jsonb', default: {} })
  performance_data: Record<string, any>;

  @CreateDateColumn({ type: 'timestamptz' })
  created_at: Date;

  // Relations
  @ManyToOne(() => MT5Account, (account) => account.performance_history, { onDelete: 'CASCADE' })
  account: MT5Account;

  // Virtual properties
  get win_rate(): number {
    if (this.total_trades === 0) return 0;
    return (this.winning_trades / this.total_trades) * 100;
  }

  get loss_rate(): number {
    if (this.total_trades === 0) return 0;
    return (this.losing_trades / this.total_trades) * 100;
  }

  get profit_factor(): number {
    const grossProfit = this.performance_data?.gross_profit || 0;
    const grossLoss = Math.abs(this.performance_data?.gross_loss || 0);
    if (grossLoss === 0) return grossProfit > 0 ? Infinity : 0;
    return grossProfit / grossLoss;
  }

  get return_percentage(): number {
    if (this.starting_balance === 0) return 0;
    return (this.daily_pnl / this.starting_balance) * 100;
  }
}