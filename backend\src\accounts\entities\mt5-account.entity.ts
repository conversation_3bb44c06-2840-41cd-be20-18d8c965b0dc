import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  Index,
  Unique,
} from 'typeorm';
import { Exclude } from 'class-transformer';
import { User } from '../../users/entities/user.entity';
import { TradingSignal } from '../../signals/entities/trading-signal.entity';
import { SignalSubscription } from '../../signals/entities/signal-subscription.entity';
import { TradeExecution } from '../../trading/entities/trade-execution.entity';
import { AccountPerformance } from './account-performance.entity';

export enum AccountType {
  MASTER = 'master',
  SLAVE = 'slave',
}

@Entity('mt5_accounts')
@Unique(['account_number', 'server_name'])
@Index(['user_id'])
@Index(['account_type'])
@Index(['is_active', 'is_connected'])
@Index(['last_heartbeat'])
export class MT5Account {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  user_id: string;

  @Column({ length: 50 })
  account_number: string;

  @Column({
    type: 'enum',
    enum: AccountType,
  })
  account_type: AccountType;

  @Column({ length: 100 })
  server_name: string;

  @Column({ length: 100 })
  broker_name: string;

  @Column({ length: 100, nullable: true })
  account_name: string;

  @Column({ length: 3, default: 'USD' })
  currency: string;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  balance: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  equity: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  margin: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  free_margin: number;

  @Column({ type: 'decimal', precision: 8, scale: 2, default: 0 })
  margin_level: number;

  @Column({ length: 255 })
  api_key: string;

  @Column({ length: 255 })
  @Exclude()
  api_secret_hash: string;

  @Column({ default: true })
  is_active: boolean;

  @Column({ default: false })
  is_connected: boolean;

  @Column({ type: 'timestamptz', nullable: true })
  last_heartbeat: Date;

  @Column({ type: 'jsonb', default: {} })
  risk_settings: Record<string, any>;

  @Column({ type: 'jsonb', default: {} })
  trading_settings: Record<string, any>;

  @CreateDateColumn({ type: 'timestamptz' })
  created_at: Date;

  @UpdateDateColumn({ type: 'timestamptz' })
  updated_at: Date;

  // Relations
  @ManyToOne(() => User, (user) => user.mt5_accounts, { onDelete: 'CASCADE' })
  user: User;

  @OneToMany(() => TradingSignal, (signal) => signal.master_account)
  generated_signals: TradingSignal[];

  @OneToMany(() => SignalSubscription, (subscription) => subscription.slave_account)
  signal_subscriptions: SignalSubscription[];

  @OneToMany(() => SignalSubscription, (subscription) => subscription.master_account)
  signal_followers: SignalSubscription[];

  @OneToMany(() => TradeExecution, (execution) => execution.slave_account)
  trade_executions: TradeExecution[];

  @OneToMany(() => AccountPerformance, (performance) => performance.account)
  performance_history: AccountPerformance[];

  // Virtual properties
  get is_online(): boolean {
    if (!this.last_heartbeat) return false;
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
    return this.last_heartbeat > fiveMinutesAgo;
  }

  get connection_status(): string {
    if (!this.is_active) return 'inactive';
    if (!this.is_connected) return 'disconnected';
    if (!this.is_online) return 'offline';
    return 'online';
  }
}