import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  Index,
  Unique,
} from 'typeorm';
import { MT5Account } from '../../accounts/entities/mt5-account.entity';

@Entity('signal_subscriptions')
@Unique(['slave_account_id', 'master_account_id'])
@Index(['slave_account_id'])
@Index(['master_account_id'])
@Index(['is_active'])
export class SignalSubscription {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  slave_account_id: string;

  @Column({ type: 'uuid' })
  master_account_id: string;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 1.0 })
  lot_multiplier: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 100.0 })
  max_lot_size: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0.01 })
  min_lot_size: number;

  @Column({ type: 'text', array: true, nullable: true })
  allowed_symbols: string[];

  @Column({ type: 'text', array: true, nullable: true })
  blocked_symbols: string[];

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 20.0 })
  max_drawdown_percent: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 5.0 })
  max_daily_loss_percent: number;

  @Column({
    type: 'jsonb',
    default: { start: '00:00', end: '23:59' }
  })
  trading_hours: Record<string, string>;

  @Column({ default: true })
  is_active: boolean;

  @CreateDateColumn({ type: 'timestamptz' })
  created_at: Date;

  @UpdateDateColumn({ type: 'timestamptz' })
  updated_at: Date;

  // Relations
  @ManyToOne(() => MT5Account, (account) => account.signal_subscriptions, { onDelete: 'CASCADE' })
  slave_account: MT5Account;

  @ManyToOne(() => MT5Account, (account) => account.signal_followers, { onDelete: 'CASCADE' })
  master_account: MT5Account;

  // Virtual properties
  get is_symbol_allowed(symbol: string): boolean {
    if (this.blocked_symbols?.includes(symbol)) return false;
    if (!this.allowed_symbols || this.allowed_symbols.length === 0) return true;
    return this.allowed_symbols.includes(symbol);
  }

  get is_trading_hours_active(): boolean {
    const now = new Date();
    const currentTime = now.toTimeString().slice(0, 5); // HH:MM format

    const startTime = this.trading_hours.start || '00:00';
    const endTime = this.trading_hours.end || '23:59';

    return currentTime >= startTime && currentTime <= endTime;
  }

  calculateLotSize(masterLotSize: number): number {
    const calculatedLot = masterLotSize * this.lot_multiplier;
    return Math.max(
      this.min_lot_size,
      Math.min(this.max_lot_size, calculatedLot)
    );
  }
}