import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  Index,
} from 'typeorm';
import { MT5Account } from '../../accounts/entities/mt5-account.entity';
import { TradeExecution } from '../../trading/entities/trade-execution.entity';

export enum SignalAction {
  OPEN = 'OPEN',
  CLOSE = 'CLOSE',
  MODIFY = 'MODIFY',
}

export enum OrderType {
  BUY = 'BUY',
  SELL = 'SELL',
  BUY_LIMIT = 'BUY_LIMIT',
  SELL_LIMIT = 'SELL_LIMIT',
  BUY_STOP = 'BUY_STOP',
  SELL_STOP = 'SELL_STOP',
}

export enum SignalStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  EXPIRED = 'expired',
}

@Entity('trading_signals')
@Index(['master_account_id'])
@Index(['symbol'])
@Index(['status'])
@Index(['created_at'])
@Index(['expires_at'])
@Index(['signal_id'])
export class TradingSignal {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 100, unique: true })
  signal_id: string;

  @Column({ type: 'uuid' })
  master_account_id: string;

  @Column({ length: 20 })
  symbol: string;

  @Column({
    type: 'enum',
    enum: SignalAction,
  })
  action: SignalAction;

  @Column({
    type: 'enum',
    enum: OrderType,
  })
  order_type: OrderType;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  volume: number;

  @Column({ type: 'decimal', precision: 10, scale: 5, nullable: true })
  price: number;

  @Column({ type: 'decimal', precision: 10, scale: 5, nullable: true })
  stop_loss: number;

  @Column({ type: 'decimal', precision: 10, scale: 5, nullable: true })
  take_profit: number;

  @Column({ type: 'text', nullable: true })
  comment: string;

  @Column({ type: 'int', nullable: true })
  magic_number: number;

  @Column({ type: 'bigint', nullable: true })
  original_ticket: number;

  @Column({ type: 'jsonb', nullable: true })
  signal_data: Record<string, any>;

  @Column({
    type: 'enum',
    enum: SignalStatus,
    default: SignalStatus.PENDING,
  })
  status: SignalStatus;

  @Column({
    type: 'timestamptz',
    default: () => "NOW() + INTERVAL '5 minutes'"
  })
  expires_at: Date;

  @CreateDateColumn({ type: 'timestamptz' })
  created_at: Date;

  @UpdateDateColumn({ type: 'timestamptz' })
  updated_at: Date;

  // Relations
  @ManyToOne(() => MT5Account, (account) => account.generated_signals)
  master_account: MT5Account;

  @OneToMany(() => TradeExecution, (execution) => execution.signal)
  executions: TradeExecution[];

  // Virtual properties
  get is_expired(): boolean {
    return new Date() > this.expires_at;
  }

  get is_active(): boolean {
    return this.status === SignalStatus.PENDING && !this.is_expired;
  }

  get execution_count(): number {
    return this.executions?.length || 0;
  }

  get successful_executions(): number {
    return this.executions?.filter(e => e.execution_status === 'executed').length || 0;
  }

  get success_rate(): number {
    if (this.execution_count === 0) return 0;
    return (this.successful_executions / this.execution_count) * 100;
  }
}