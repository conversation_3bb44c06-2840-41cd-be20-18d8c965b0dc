# MT5 Copy Trading System - MQL5 Implementation Requirements

## HTTP Client Implementation

### WebRequest Configuration
```mql5
// HTTP client class for EA communication
class HttpClient {
private:
    string m_baseUrl;
    string m_apiKey;
    int m_timeout;
    string m_userAgent;

public:
    HttpClient(string baseUrl, string apiKey, int timeout = 5000) {
        m_baseUrl = baseUrl;
        m_apiKey = apiKey;
        m_timeout = timeout;
        m_userAgent = "MT5-CopyTrading-EA/1.0";
    }

    // POST request with JSON payload
    bool PostJson(string endpoint, string jsonData, string &response) {
        string url = m_baseUrl + endpoint;
        string headers = "Content-Type: application/json\r\n";
        headers += "Authorization: Bearer " + m_apiKey + "\r\n";
        headers += "User-Agent: " + m_userAgent + "\r\n";

        char data[];
        StringToCharArray(jsonData, data, 0, StringLen(jsonData));

        char result[];
        string resultHeaders;

        int res = WebRequest("POST", url, headers, m_timeout, data, result, resultHeaders);

        if (res == 200) {
            response = CharArrayToString(result);
            return true;
        } else {
            PrintFormat("HTTP POST failed: %d", res);
            return false;
        }
    }

    // GET request with query parameters
    bool Get(string endpoint, string queryParams, string &response) {
        string url = m_baseUrl + endpoint;
        if (StringLen(queryParams) > 0) {
            url += "?" + queryParams;
        }

        string headers = "Authorization: Bearer " + m_apiKey + "\r\n";
        headers += "User-Agent: " + m_userAgent + "\r\n";

        char result[];
        string resultHeaders;

        int res = WebRequest("GET", url, headers, m_timeout, NULL, result, resultHeaders);

        if (res == 200) {
            response = CharArrayToString(result);
            return true;
        } else {
            PrintFormat("HTTP GET failed: %d", res);
            return false;
        }
    }

    // PUT request for updates
    bool Put(string endpoint, string jsonData, string &response) {
        string url = m_baseUrl + endpoint;
        string headers = "Content-Type: application/json\r\n";
        headers += "Authorization: Bearer " + m_apiKey + "\r\n";
        headers += "User-Agent: " + m_userAgent + "\r\n";

        char data[];
        StringToCharArray(jsonData, data, 0, StringLen(jsonData));

        char result[];
        string resultHeaders;

        int res = WebRequest("PUT", url, headers, m_timeout, data, result, resultHeaders);

        if (res == 200 || res == 204) {
            response = CharArrayToString(result);
            return true;
        } else {
            PrintFormat("HTTP PUT failed: %d", res);
            return false;
        }
    }
};
```

### Network Timeout & Retry Mechanisms
```mql5
// Retry mechanism with exponential backoff
class RetryHandler {
private:
    int m_maxRetries;
    int m_baseDelay;
    double m_backoffMultiplier;

public:
    RetryHandler(int maxRetries = 3, int baseDelay = 1000, double backoffMultiplier = 2.0) {
        m_maxRetries = maxRetries;
        m_baseDelay = baseDelay;
        m_backoffMultiplier = backoffMultiplier;
    }

    template<typename T>
    bool ExecuteWithRetry(T &operation, string operationName) {
        int attempt = 0;
        int delay = m_baseDelay;

        while (attempt < m_maxRetries) {
            attempt++;

            PrintFormat("Attempting %s (attempt %d/%d)", operationName, attempt, m_maxRetries);

            if (operation.Execute()) {
                PrintFormat("%s succeeded on attempt %d", operationName, attempt);
                return true;
            }

            if (attempt < m_maxRetries) {
                PrintFormat("%s failed, retrying in %d ms", operationName, delay);
                Sleep(delay);
                delay = (int)(delay * m_backoffMultiplier);
            }
        }

        PrintFormat("%s failed after %d attempts", operationName, m_maxRetries);
        return false;
    }
};

// Network operation wrapper
class NetworkOperation {
private:
    HttpClient* m_httpClient;
    string m_endpoint;
    string m_data;
    string m_method;

public:
    string Response;

    NetworkOperation(HttpClient* client, string method, string endpoint, string data = "") {
        m_httpClient = client;
        m_method = method;
        m_endpoint = endpoint;
        m_data = data;
    }

    bool Execute() {
        if (m_method == "POST") {
            return m_httpClient.PostJson(m_endpoint, m_data, Response);
        } else if (m_method == "GET") {
            return m_httpClient.Get(m_endpoint, m_data, Response);
        } else if (m_method == "PUT") {
            return m_httpClient.Put(m_endpoint, m_data, Response);
        }
        return false;
    }
};
```

## JSON Parsing and Serialization

### JSON Helper Class
```mql5
// JSON utility class for MQL5
class JsonHelper {
public:
    // Create JSON string from signal data
    static string CreateSignalJson(const SignalData &signal) {
        string json = "{";
        json += "\"signal_id\":\"" + signal.SignalId + "\",";
        json += "\"master_account\":\"" + signal.MasterAccount + "\",";
        json += "\"symbol\":\"" + signal.Symbol + "\",";
        json += "\"action\":\"" + signal.Action + "\",";
        json += "\"order_type\":\"" + signal.OrderType + "\",";
        json += "\"volume\":" + DoubleToString(signal.Volume, 2) + ",";

        if (signal.Price > 0) {
            json += "\"price\":" + DoubleToString(signal.Price, 5) + ",";
        }
        if (signal.StopLoss > 0) {
            json += "\"stop_loss\":" + DoubleToString(signal.StopLoss, 5) + ",";
        }
        if (signal.TakeProfit > 0) {
            json += "\"take_profit\":" + DoubleToString(signal.TakeProfit, 5) + ",";
        }
        if (StringLen(signal.Comment) > 0) {
            json += "\"comment\":\"" + signal.Comment + "\",";
        }
        if (signal.MagicNumber > 0) {
            json += "\"magic_number\":" + IntegerToString(signal.MagicNumber) + ",";
        }
        if (signal.OriginalTicket > 0) {
            json += "\"original_ticket\":" + IntegerToString(signal.OriginalTicket) + ",";
        }

        // Remove trailing comma
        if (StringGetCharacter(json, StringLen(json) - 1) == ',') {
            json = StringSubstr(json, 0, StringLen(json) - 1);
        }

        json += "}";
        return json;
    }

    // Parse JSON response to extract signals
    static bool ParseSignalsResponse(string jsonResponse, SignalData &signals[]) {
        // Simple JSON parsing for signals array
        int signalsStart = StringFind(jsonResponse, "\"signals\":[");
        if (signalsStart == -1) return false;

        signalsStart += 11; // Length of "\"signals\":["
        int signalsEnd = StringFind(jsonResponse, "]", signalsStart);
        if (signalsEnd == -1) return false;

        string signalsJson = StringSubstr(jsonResponse, signalsStart, signalsEnd - signalsStart);

        // Parse individual signals (simplified implementation)
        return ParseSignalArray(signalsJson, signals);
    }

    // Extract string value from JSON
    static string ExtractStringValue(string json, string key) {
        string searchKey = "\"" + key + "\":\"";
        int start = StringFind(json, searchKey);
        if (start == -1) return "";

        start += StringLen(searchKey);
        int end = StringFind(json, "\"", start);
        if (end == -1) return "";

        return StringSubstr(json, start, end - start);
    }

    // Extract numeric value from JSON
    static double ExtractNumericValue(string json, string key) {
        string searchKey = "\"" + key + "\":";
        int start = StringFind(json, searchKey);
        if (start == -1) return 0;

        start += StringLen(searchKey);
        int end = StringFind(json, ",", start);
        if (end == -1) end = StringFind(json, "}", start);
        if (end == -1) return 0;

        string valueStr = StringSubstr(json, start, end - start);
        return StringToDouble(valueStr);
    }

private:
    static bool ParseSignalArray(string signalsJson, SignalData &signals[]) {
        // Implementation for parsing signal array
        // This is a simplified version - in production, use a proper JSON library
        ArrayResize(signals, 0);

        int pos = 0;
        while (pos < StringLen(signalsJson)) {
            int objStart = StringFind(signalsJson, "{", pos);
            if (objStart == -1) break;

            int objEnd = StringFind(signalsJson, "}", objStart);
            if (objEnd == -1) break;

            string signalJson = StringSubstr(signalsJson, objStart, objEnd - objStart + 1);

            SignalData signal;
            if (ParseSingleSignal(signalJson, signal)) {
                int size = ArraySize(signals);
                ArrayResize(signals, size + 1);
                signals[size] = signal;
            }

            pos = objEnd + 1;
        }

        return ArraySize(signals) > 0;
    }

    static bool ParseSingleSignal(string signalJson, SignalData &signal) {
        signal.SignalId = ExtractStringValue(signalJson, "signal_id");
        signal.MasterAccount = ExtractStringValue(signalJson, "master_account");
        signal.Symbol = ExtractStringValue(signalJson, "symbol");
        signal.Action = ExtractStringValue(signalJson, "action");
        signal.OrderType = ExtractStringValue(signalJson, "order_type");
        signal.Volume = ExtractNumericValue(signalJson, "volume");
        signal.Price = ExtractNumericValue(signalJson, "price");
        signal.StopLoss = ExtractNumericValue(signalJson, "stop_loss");
        signal.TakeProfit = ExtractNumericValue(signalJson, "take_profit");
        signal.Comment = ExtractStringValue(signalJson, "comment");
        signal.MagicNumber = (int)ExtractNumericValue(signalJson, "magic_number");
        signal.OriginalTicket = (long)ExtractNumericValue(signalJson, "original_ticket");

        return StringLen(signal.SignalId) > 0;
    }
};
```

## Error Management & Logging

### Comprehensive Error Handling
```mql5
// Error codes for copy trading system
enum CopyTradingError {
    CT_SUCCESS = 0,
    CT_NETWORK_ERROR = 1001,
    CT_AUTH_ERROR = 1002,
    CT_VALIDATION_ERROR = 1003,
    CT_TIMEOUT_ERROR = 1004,
    CT_TRADE_ERROR = 1005,
    CT_INSUFFICIENT_MARGIN = 1006,
    CT_SYMBOL_NOT_FOUND = 1007,
    CT_INVALID_VOLUME = 1008,
    CT_MARKET_CLOSED = 1009,
    CT_JSON_PARSE_ERROR = 1010
};

// Error handler class
class ErrorHandler {
private:
    string m_logFile;
    bool m_enableLogging;

public:
    ErrorHandler(string logFile = "copytrading_errors.log", bool enableLogging = true) {
        m_logFile = logFile;
        m_enableLogging = enableLogging;
    }

    void LogError(CopyTradingError errorCode, string message, string context = "") {
        string errorMsg = GetErrorDescription(errorCode) + ": " + message;
        if (StringLen(context) > 0) {
            errorMsg += " [Context: " + context + "]";
        }

        // Print to terminal
        PrintFormat("ERROR %d: %s", errorCode, errorMsg);

        // Write to log file
        if (m_enableLogging) {
            WriteToLogFile(errorCode, errorMsg);
        }
    }

    void LogWarning(string message, string context = "") {
        string warnMsg = "WARNING: " + message;
        if (StringLen(context) > 0) {
            warnMsg += " [Context: " + context + "]";
        }

        Print(warnMsg);

        if (m_enableLogging) {
            WriteToLogFile(0, warnMsg);
        }
    }

    void LogInfo(string message, string context = "") {
        string infoMsg = "INFO: " + message;
        if (StringLen(context) > 0) {
            infoMsg += " [Context: " + context + "]";
        }

        Print(infoMsg);

        if (m_enableLogging) {
            WriteToLogFile(0, infoMsg);
        }
    }

    string GetErrorDescription(CopyTradingError errorCode) {
        switch (errorCode) {
            case CT_SUCCESS: return "Success";
            case CT_NETWORK_ERROR: return "Network Error";
            case CT_AUTH_ERROR: return "Authentication Error";
            case CT_VALIDATION_ERROR: return "Validation Error";
            case CT_TIMEOUT_ERROR: return "Timeout Error";
            case CT_TRADE_ERROR: return "Trade Execution Error";
            case CT_INSUFFICIENT_MARGIN: return "Insufficient Margin";
            case CT_SYMBOL_NOT_FOUND: return "Symbol Not Found";
            case CT_INVALID_VOLUME: return "Invalid Volume";
            case CT_MARKET_CLOSED: return "Market Closed";
            case CT_JSON_PARSE_ERROR: return "JSON Parse Error";
            default: return "Unknown Error";
        }
    }

private:
    void WriteToLogFile(int errorCode, string message) {
        int fileHandle = FileOpen(m_logFile, FILE_WRITE | FILE_TXT | FILE_ANSI);
        if (fileHandle != INVALID_HANDLE) {
            string timestamp = TimeToString(TimeCurrent(), TIME_DATE | TIME_SECONDS);
            string logEntry = StringFormat("[%s] Code:%d %s\n", timestamp, errorCode, message);
            FileWrite(fileHandle, logEntry);
            FileClose(fileHandle);
        }
    }
};
```

### Duplicate Signal Prevention
```mql5
// Signal deduplication manager
class SignalDeduplicator {
private:
    string m_processedSignals[];
    int m_maxHistorySize;
    datetime m_lastCleanup;
    int m_cleanupInterval; // seconds

public:
    SignalDeduplicator(int maxHistorySize = 1000, int cleanupInterval = 3600) {
        m_maxHistorySize = maxHistorySize;
        m_cleanupInterval = cleanupInterval;
        m_lastCleanup = TimeCurrent();
        ArrayResize(m_processedSignals, 0);
    }

    bool IsSignalProcessed(string signalId) {
        // Check if signal was already processed
        for (int i = 0; i < ArraySize(m_processedSignals); i++) {
            if (m_processedSignals[i] == signalId) {
                return true;
            }
        }
        return false;
    }

    void MarkSignalProcessed(string signalId) {
        // Add signal to processed list
        int size = ArraySize(m_processedSignals);
        ArrayResize(m_processedSignals, size + 1);
        m_processedSignals[size] = signalId;

        // Cleanup if necessary
        if (size >= m_maxHistorySize ||
            TimeCurrent() - m_lastCleanup > m_cleanupInterval) {
            CleanupOldSignals();
        }
    }

private:
    void CleanupOldSignals() {
        // Keep only the most recent signals
        int currentSize = ArraySize(m_processedSignals);
        if (currentSize > m_maxHistorySize / 2) {
            int keepCount = m_maxHistorySize / 2;
            string tempArray[];
            ArrayResize(tempArray, keepCount);

            // Copy the most recent signals
            for (int i = 0; i < keepCount; i++) {
                tempArray[i] = m_processedSignals[currentSize - keepCount + i];
            }

            // Replace the array
            ArrayCopy(m_processedSignals, tempArray);
        }

        m_lastCleanup = TimeCurrent();
    }
};
```

### Memory Management Best Practices
```mql5
// Memory-efficient signal queue
class SignalQueue {
private:
    SignalData m_signals[];
    int m_head;
    int m_tail;
    int m_size;
    int m_capacity;

public:
    SignalQueue(int capacity = 100) {
        m_capacity = capacity;
        m_head = 0;
        m_tail = 0;
        m_size = 0;
        ArrayResize(m_signals, capacity);
    }

    ~SignalQueue() {
        ArrayFree(m_signals);
    }

    bool Enqueue(const SignalData &signal) {
        if (m_size >= m_capacity) {
            // Queue is full, remove oldest signal
            Dequeue();
        }

        m_signals[m_tail] = signal;
        m_tail = (m_tail + 1) % m_capacity;
        m_size++;

        return true;
    }

    bool Dequeue(SignalData &signal) {
        if (m_size == 0) {
            return false; // Queue is empty
        }

        signal = m_signals[m_head];
        m_head = (m_head + 1) % m_capacity;
        m_size--;

        return true;
    }

    bool Dequeue() {
        if (m_size == 0) {
            return false;
        }

        m_head = (m_head + 1) % m_capacity;
        m_size--;

        return true;
    }

    int Size() const { return m_size; }
    bool IsEmpty() const { return m_size == 0; }
    bool IsFull() const { return m_size >= m_capacity; }

    // Peek at the front signal without removing it
    bool Peek(SignalData &signal) const {
        if (m_size == 0) {
            return false;
        }

        signal = m_signals[m_head];
        return true;
    }
};
```

## Data Structures & Signal Management

### Signal Data Structure
```mql5
// Core signal data structure
struct SignalData {
    string SignalId;
    string MasterAccount;
    string Symbol;
    string Action;          // OPEN, CLOSE, MODIFY
    string OrderType;       // BUY, SELL, BUY_LIMIT, etc.
    double Volume;
    double Price;
    double StopLoss;
    double TakeProfit;
    string Comment;
    int MagicNumber;
    long OriginalTicket;
    datetime Timestamp;
    datetime ExpiresAt;

    // Constructor
    SignalData() {
        SignalId = "";
        MasterAccount = "";
        Symbol = "";
        Action = "";
        OrderType = "";
        Volume = 0.0;
        Price = 0.0;
        StopLoss = 0.0;
        TakeProfit = 0.0;
        Comment = "";
        MagicNumber = 0;
        OriginalTicket = 0;
        Timestamp = 0;
        ExpiresAt = 0;
    }

    // Copy constructor
    SignalData(const SignalData &other) {
        SignalId = other.SignalId;
        MasterAccount = other.MasterAccount;
        Symbol = other.Symbol;
        Action = other.Action;
        OrderType = other.OrderType;
        Volume = other.Volume;
        Price = other.Price;
        StopLoss = other.StopLoss;
        TakeProfit = other.TakeProfit;
        Comment = other.Comment;
        MagicNumber = other.MagicNumber;
        OriginalTicket = other.OriginalTicket;
        Timestamp = other.Timestamp;
        ExpiresAt = other.ExpiresAt;
    }

    // Validation
    bool IsValid() const {
        return StringLen(SignalId) > 0 &&
               StringLen(Symbol) > 0 &&
               Volume > 0 &&
               StringLen(Action) > 0 &&
               StringLen(OrderType) > 0;
    }

    // Check if signal has expired
    bool IsExpired() const {
        return ExpiresAt > 0 && TimeCurrent() > ExpiresAt;
    }
};

// Execution result structure
struct ExecutionResult {
    string SignalId;
    string SlaveAccount;
    bool Success;
    long Ticket;
    double ExecutedPrice;
    double ExecutedVolume;
    int ErrorCode;
    string ErrorMessage;
    datetime ExecutionTime;

    ExecutionResult() {
        SignalId = "";
        SlaveAccount = "";
        Success = false;
        Ticket = 0;
        ExecutedPrice = 0.0;
        ExecutedVolume = 0.0;
        ErrorCode = 0;
        ErrorMessage = "";
        ExecutionTime = 0;
    }
};
```

### Performance Monitoring
```mql5
// Performance metrics collector
class PerformanceMonitor {
private:
    int m_signalsReceived;
    int m_signalsProcessed;
    int m_signalsExecuted;
    int m_signalsFailed;
    datetime m_startTime;
    double m_totalLatency;
    int m_latencyCount;

public:
    PerformanceMonitor() {
        Reset();
    }

    void Reset() {
        m_signalsReceived = 0;
        m_signalsProcessed = 0;
        m_signalsExecuted = 0;
        m_signalsFailed = 0;
        m_startTime = TimeCurrent();
        m_totalLatency = 0.0;
        m_latencyCount = 0;
    }

    void OnSignalReceived() {
        m_signalsReceived++;
    }

    void OnSignalProcessed() {
        m_signalsProcessed++;
    }

    void OnSignalExecuted(double latencySeconds) {
        m_signalsExecuted++;
        m_totalLatency += latencySeconds;
        m_latencyCount++;
    }

    void OnSignalFailed() {
        m_signalsFailed++;
    }

    void PrintStatistics() {
        datetime currentTime = TimeCurrent();
        int runtimeSeconds = (int)(currentTime - m_startTime);

        double successRate = m_signalsReceived > 0 ?
            (double)m_signalsExecuted / m_signalsReceived * 100.0 : 0.0;

        double avgLatency = m_latencyCount > 0 ?
            m_totalLatency / m_latencyCount : 0.0;

        PrintFormat("=== Performance Statistics ===");
        PrintFormat("Runtime: %d seconds", runtimeSeconds);
        PrintFormat("Signals Received: %d", m_signalsReceived);
        PrintFormat("Signals Processed: %d", m_signalsProcessed);
        PrintFormat("Signals Executed: %d", m_signalsExecuted);
        PrintFormat("Signals Failed: %d", m_signalsFailed);
        PrintFormat("Success Rate: %.2f%%", successRate);
        PrintFormat("Average Latency: %.3f seconds", avgLatency);

        if (runtimeSeconds > 0) {
            double signalsPerMinute = (double)m_signalsReceived / runtimeSeconds * 60.0;
            PrintFormat("Signals per minute: %.2f", signalsPerMinute);
        }
    }

    // Getters for external monitoring
    int GetSignalsReceived() const { return m_signalsReceived; }
    int GetSignalsExecuted() const { return m_signalsExecuted; }
    int GetSignalsFailed() const { return m_signalsFailed; }
    double GetSuccessRate() const {
        return m_signalsReceived > 0 ?
            (double)m_signalsExecuted / m_signalsReceived * 100.0 : 0.0;
    }
    double GetAverageLatency() const {
        return m_latencyCount > 0 ? m_totalLatency / m_latencyCount : 0.0;
    }
};
```