# MT5 Copy Trading System - Scalability & Performance Solutions

## Horizontal Scaling Architecture

### Load Balancer Configuration
```nginx
# nginx.conf for high-performance load balancing
upstream backend_api {
    least_conn;
    server backend-1:3000 weight=3 max_fails=3 fail_timeout=30s;
    server backend-2:3000 weight=3 max_fails=3 fail_timeout=30s;
    server backend-3:3000 weight=2 max_fails=3 fail_timeout=30s;
    keepalive 32;
}

upstream websocket_servers {
    ip_hash;  # Sticky sessions for WebSocket connections
    server backend-1:3000;
    server backend-2:3000;
    server backend-3:3000;
}

server {
    listen 80;
    listen 443 ssl http2;
    server_name api.copytrading.com;

    # SSL configuration
    ssl_certificate /etc/ssl/certs/copytrading.crt;
    ssl_certificate_key /etc/ssl/private/copytrading.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

    # Performance optimizations
    client_max_body_size 10M;
    client_body_timeout 60s;
    client_header_timeout 60s;
    keepalive_timeout 65s;
    send_timeout 60s;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain application/json application/javascript text/css;

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=100r/m;
    limit_req_zone $binary_remote_addr zone=signals:10m rate=200r/m;

    # API endpoints
    location /api/ {
        limit_req zone=api burst=20 nodelay;
        proxy_pass http://backend_api;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_connect_timeout 5s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # Signal endpoints with higher rate limits
    location /api/signals {
        limit_req zone=signals burst=50 nodelay;
        proxy_pass http://backend_api;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_connect_timeout 2s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    # WebSocket connections
    location /socket.io/ {
        proxy_pass http://websocket_servers;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_connect_timeout 7d;
        proxy_send_timeout 7d;
        proxy_read_timeout 7d;
    }
}
```

### Auto-scaling with Kubernetes
```yaml
# kubernetes/backend-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: copytrading-backend
  labels:
    app: copytrading-backend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: copytrading-backend
  template:
    metadata:
      labels:
        app: copytrading-backend
    spec:
      containers:
      - name: backend
        image: copytrading/backend:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: redis-secret
              key: url
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: copytrading-backend-service
spec:
  selector:
    app: copytrading-backend
  ports:
  - protocol: TCP
    port: 80
    targetPort: 3000
  type: ClusterIP

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: copytrading-backend-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: copytrading-backend
  minReplicas: 3
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 15
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
```

## Redis Pub/Sub Implementation

### Signal Distribution Architecture
```typescript
// Redis Pub/Sub service for real-time signal distribution
@Injectable()
export class SignalDistributionService {
  private publisher: Redis;
  private subscriber: Redis;

  constructor(
    @Inject('REDIS_PUBLISHER') publisher: Redis,
    @Inject('REDIS_SUBSCRIBER') subscriber: Redis,
    private signalsGateway: SignalsGateway
  ) {
    this.publisher = publisher;
    this.subscriber = subscriber;
    this.setupSubscriptions();
  }

  private setupSubscriptions() {
    // Subscribe to signal channels
    this.subscriber.psubscribe('signals:*');
    this.subscriber.psubscribe('executions:*');
    this.subscriber.psubscribe('accounts:*');

    this.subscriber.on('pmessage', (pattern, channel, message) => {
      this.handleMessage(pattern, channel, message);
    });
  }

  private async handleMessage(pattern: string, channel: string, message: string) {
    try {
      const data = JSON.parse(message);

      switch (pattern) {
        case 'signals:*':
          await this.handleSignalMessage(channel, data);
          break;
        case 'executions:*':
          await this.handleExecutionMessage(channel, data);
          break;
        case 'accounts:*':
          await this.handleAccountMessage(channel, data);
          break;
      }
    } catch (error) {
      console.error('Error handling Redis message:', error);
    }
  }

  async publishSignal(signal: TradingSignal) {
    const channel = `signals:${signal.master_account_id}`;
    const message = JSON.stringify({
      type: 'new_signal',
      data: signal,
      timestamp: new Date().toISOString()
    });

    // Publish to Redis
    await this.publisher.publish(channel, message);

    // Also broadcast via WebSocket
    this.signalsGateway.broadcastSignal(signal);
  }

  async publishExecution(execution: TradeExecution) {
    const channel = `executions:${execution.signal_id}`;
    const message = JSON.stringify({
      type: 'execution_update',
      data: execution,
      timestamp: new Date().toISOString()
    });

    await this.publisher.publish(channel, message);
    this.signalsGateway.broadcastExecution(execution);
  }

  private async handleSignalMessage(channel: string, data: any) {
    // Process signal for subscribers
    const masterAccountId = channel.split(':')[1];
    const subscribers = await this.getSignalSubscribers(masterAccountId);

    for (const subscriber of subscribers) {
      await this.processSignalForSubscriber(data.data, subscriber);
    }
  }
}
```

### Redis Cluster Configuration
```yaml
# redis-cluster.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: redis-cluster-config
data:
  redis.conf: |
    cluster-enabled yes
    cluster-config-file nodes.conf
    cluster-node-timeout 5000
    appendonly yes
    appendfsync everysec

    # Memory optimization
    maxmemory 2gb
    maxmemory-policy allkeys-lru

    # Network optimization
    tcp-keepalive 300
    timeout 0

    # Pub/Sub optimization
    client-output-buffer-limit pubsub 32mb 8mb 60

    # Persistence
    save 900 1
    save 300 10
    save 60 10000

---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: redis-cluster
spec:
  serviceName: redis-cluster
  replicas: 6
  selector:
    matchLabels:
      app: redis-cluster
  template:
    metadata:
      labels:
        app: redis-cluster
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        ports:
        - containerPort: 6379
        - containerPort: 16379
        command:
        - redis-server
        - /etc/redis/redis.conf
        volumeMounts:
        - name: config
          mountPath: /etc/redis
        - name: data
          mountPath: /data
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
      volumes:
      - name: config
        configMap:
          name: redis-cluster-config
  volumeClaimTemplates:
  - metadata:
      name: data
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 10Gi
```

## Advanced Caching Strategies

### Multi-Level Caching Architecture
```typescript
// Multi-level cache implementation
@Injectable()
export class CacheService {
  private l1Cache: Map<string, any> = new Map(); // In-memory cache
  private l2Cache: Redis; // Redis cache

  constructor(@Inject('REDIS_CLIENT') redis: Redis) {
    this.l2Cache = redis;

    // L1 cache cleanup every 5 minutes
    setInterval(() => this.cleanupL1Cache(), 5 * 60 * 1000);
  }

  async get<T>(key: string): Promise<T | null> {
    // Try L1 cache first
    if (this.l1Cache.has(key)) {
      const cached = this.l1Cache.get(key);
      if (cached.expires > Date.now()) {
        return cached.value;
      }
      this.l1Cache.delete(key);
    }

    // Try L2 cache (Redis)
    const redisValue = await this.l2Cache.get(key);
    if (redisValue) {
      const parsed = JSON.parse(redisValue);

      // Store in L1 cache for faster access
      this.l1Cache.set(key, {
        value: parsed,
        expires: Date.now() + 60000 // 1 minute L1 TTL
      });

      return parsed;
    }

    return null;
  }

  async set<T>(key: string, value: T, ttlSeconds: number = 300): Promise<void> {
    // Store in both caches
    this.l1Cache.set(key, {
      value,
      expires: Date.now() + Math.min(ttlSeconds * 1000, 60000) // Max 1 min L1
    });

    await this.l2Cache.setex(key, ttlSeconds, JSON.stringify(value));
  }

  async invalidate(pattern: string): Promise<void> {
    // Clear L1 cache
    for (const key of this.l1Cache.keys()) {
      if (key.includes(pattern)) {
        this.l1Cache.delete(key);
      }
    }

    // Clear L2 cache
    const keys = await this.l2Cache.keys(pattern);
    if (keys.length > 0) {
      await this.l2Cache.del(...keys);
    }
  }

  private cleanupL1Cache(): void {
    const now = Date.now();
    for (const [key, cached] of this.l1Cache.entries()) {
      if (cached.expires <= now) {
        this.l1Cache.delete(key);
      }
    }
  }
}
```

### Cache-Aside Pattern Implementation
```typescript
// Cache-aside pattern for signal data
@Injectable()
export class SignalCacheService {
  constructor(
    private cacheService: CacheService,
    private signalsRepository: Repository<TradingSignal>
  ) {}

  async getSignalsForAccount(accountId: string, limit: number = 50): Promise<TradingSignal[]> {
    const cacheKey = `signals:pending:${accountId}:${limit}`;

    // Try cache first
    let signals = await this.cacheService.get<TradingSignal[]>(cacheKey);

    if (!signals) {
      // Cache miss - fetch from database
      signals = await this.signalsRepository.find({
        where: {
          master_account_id: accountId,
          status: 'pending',
          expires_at: MoreThan(new Date())
        },
        order: { created_at: 'ASC' },
        take: limit
      });

      // Cache for 30 seconds
      await this.cacheService.set(cacheKey, signals, 30);
    }

    return signals;
  }

  async invalidateAccountSignals(accountId: string): Promise<void> {
    await this.cacheService.invalidate(`signals:pending:${accountId}:*`);
  }
}
```

## Monitoring & Observability Setup

### Prometheus Metrics Configuration
```typescript
// Custom metrics for trading system
import { register, Counter, Histogram, Gauge } from 'prom-client';

// Signal processing metrics
export const signalProcessingCounter = new Counter({
  name: 'signals_processed_total',
  help: 'Total number of signals processed',
  labelNames: ['master_account', 'symbol', 'action', 'status']
});

export const signalProcessingDuration = new Histogram({
  name: 'signal_processing_duration_seconds',
  help: 'Time taken to process signals',
  labelNames: ['master_account', 'action'],
  buckets: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 2, 5]
});

// Execution metrics
export const executionCounter = new Counter({
  name: 'executions_total',
  help: 'Total number of trade executions',
  labelNames: ['slave_account', 'symbol', 'status']
});

export const executionLatency = new Histogram({
  name: 'execution_latency_seconds',
  help: 'Latency from signal to execution',
  labelNames: ['slave_account', 'symbol'],
  buckets: [0.1, 0.5, 1, 2, 5, 10, 30, 60]
});

// System metrics
export const activeConnections = new Gauge({
  name: 'active_connections',
  help: 'Number of active WebSocket connections',
  labelNames: ['type']
});

export const redisOperations = new Counter({
  name: 'redis_operations_total',
  help: 'Total Redis operations',
  labelNames: ['operation', 'status']
});

// Database metrics
export const dbQueryDuration = new Histogram({
  name: 'db_query_duration_seconds',
  help: 'Database query execution time',
  labelNames: ['query_type', 'table'],
  buckets: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 2]
});

// Register all metrics
register.registerMetric(signalProcessingCounter);
register.registerMetric(signalProcessingDuration);
register.registerMetric(executionCounter);
register.registerMetric(executionLatency);
register.registerMetric(activeConnections);
register.registerMetric(redisOperations);
register.registerMetric(dbQueryDuration);
```

### Grafana Dashboard Configuration
```json
{
  "dashboard": {
    "title": "MT5 Copy Trading System",
    "panels": [
      {
        "title": "Signal Processing Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(signals_processed_total[5m])",
            "legendFormat": "{{master_account}} - {{action}}"
          }
        ]
      },
      {
        "title": "Execution Success Rate",
        "type": "stat",
        "targets": [
          {
            "expr": "rate(executions_total{status=\"executed\"}[5m]) / rate(executions_total[5m]) * 100",
            "legendFormat": "Success Rate %"
          }
        ]
      },
      {
        "title": "Signal to Execution Latency",
        "type": "heatmap",
        "targets": [
          {
            "expr": "execution_latency_seconds_bucket",
            "legendFormat": "{{le}}"
          }
        ]
      },
      {
        "title": "Active Connections",
        "type": "graph",
        "targets": [
          {
            "expr": "active_connections",
            "legendFormat": "{{type}}"
          }
        ]
      },
      {
        "title": "Database Performance",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(db_query_duration_seconds_sum[5m]) / rate(db_query_duration_seconds_count[5m])",
            "legendFormat": "{{query_type}} - {{table}}"
          }
        ]
      },
      {
        "title": "Redis Operations",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(redis_operations_total[5m])",
            "legendFormat": "{{operation}} - {{status}}"
          }
        ]
      }
    ]
  }
}
```

### Application Performance Monitoring
```typescript
// APM integration with custom interceptor
@Injectable()
export class MetricsInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const response = context.switchToHttp().getResponse();
    const startTime = Date.now();

    return next.handle().pipe(
      tap(() => {
        const duration = (Date.now() - startTime) / 1000;
        const route = request.route?.path || request.url;
        const method = request.method;
        const statusCode = response.statusCode;

        // Record HTTP metrics
        httpRequestDuration.observe(
          { method, route, status_code: statusCode },
          duration
        );

        httpRequestsTotal.inc({
          method,
          route,
          status_code: statusCode
        });
      }),
      catchError((error) => {
        const duration = (Date.now() - startTime) / 1000;
        const route = request.route?.path || request.url;
        const method = request.method;

        httpRequestDuration.observe(
          { method, route, status_code: 500 },
          duration
        );

        httpRequestsTotal.inc({
          method,
          route,
          status_code: 500
        });

        throw error;
      })
    );
  }
}
```

## CDN Configuration for Frontend Assets

### CloudFlare Configuration
```javascript
// cloudflare-worker.js for edge caching
addEventListener('fetch', event => {
  event.respondWith(handleRequest(event.request));
});

async function handleRequest(request) {
  const url = new URL(request.url);

  // Cache static assets aggressively
  if (url.pathname.match(/\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2)$/)) {
    const cache = caches.default;
    const cacheKey = new Request(url.toString(), request);

    let response = await cache.match(cacheKey);

    if (!response) {
      response = await fetch(request);

      if (response.status === 200) {
        const headers = new Headers(response.headers);
        headers.set('Cache-Control', 'public, max-age=31536000'); // 1 year
        headers.set('Vary', 'Accept-Encoding');

        response = new Response(response.body, {
          status: response.status,
          statusText: response.statusText,
          headers: headers
        });

        event.waitUntil(cache.put(cacheKey, response.clone()));
      }
    }

    return response;
  }

  // Cache API responses for short periods
  if (url.pathname.startsWith('/api/')) {
    const cache = caches.default;
    const cacheKey = new Request(url.toString(), request);

    let response = await cache.match(cacheKey);

    if (!response) {
      response = await fetch(request);

      if (response.status === 200 && request.method === 'GET') {
        const headers = new Headers(response.headers);
        headers.set('Cache-Control', 'public, max-age=60'); // 1 minute

        response = new Response(response.body, {
          status: response.status,
          statusText: response.statusText,
          headers: headers
        });

        event.waitUntil(cache.put(cacheKey, response.clone()));
      }
    }

    return response;
  }

  return fetch(request);
}
```

### Performance Optimization Checklist

#### Backend Optimizations
```typescript
// Connection pooling optimization
const dbConfig = {
  // PostgreSQL
  postgres: {
    max: 20,                    // Max connections
    min: 5,                     // Min connections
    acquire: 30000,             // Max time to get connection
    idle: 10000,                // Max idle time
    evict: 1000,                // Eviction interval
    handleDisconnects: true,
    testOnBorrow: true,
    validationQuery: 'SELECT 1'
  },

  // Redis
  redis: {
    lazyConnect: true,
    maxRetriesPerRequest: 3,
    retryDelayOnFailover: 100,
    enableReadyCheck: false,
    maxLoadingTimeout: 5000,
    connectTimeout: 10000,
    commandTimeout: 5000,
    keepAlive: true
  }
};

// Query optimization
@Injectable()
export class OptimizedSignalsService {
  // Use database indexes effectively
  async getSignalsOptimized(accountId: string, limit: number) {
    return this.signalsRepository
      .createQueryBuilder('signal')
      .where('signal.master_account_id = :accountId', { accountId })
      .andWhere('signal.status = :status', { status: 'pending' })
      .andWhere('signal.expires_at > :now', { now: new Date() })
      .orderBy('signal.created_at', 'ASC')
      .limit(limit)
      .getMany();
  }

  // Batch processing for better performance
  async processSignalsBatch(signals: TradingSignal[]) {
    const batchSize = 100;
    const batches = [];

    for (let i = 0; i < signals.length; i += batchSize) {
      batches.push(signals.slice(i, i + batchSize));
    }

    await Promise.all(
      batches.map(batch => this.processBatch(batch))
    );
  }
}
```