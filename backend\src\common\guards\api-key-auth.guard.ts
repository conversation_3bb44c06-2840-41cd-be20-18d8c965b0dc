import { Injectable, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';

@Injectable()
export class ApiKeyAuthGuard extends AuthGuard('api-key') {
  canActivate(context: ExecutionContext) {
    return super.canActivate(context);
  }

  handleRequest(err: any, account: any, info: any, context: ExecutionContext) {
    if (err || !account) {
      throw err || new UnauthorizedException('Invalid API key');
    }
    return account;
  }
}