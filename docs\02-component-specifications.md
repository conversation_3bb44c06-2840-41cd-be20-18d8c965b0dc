# MT5 Copy Trading System - Component Specifications

## A) EA MASTER (MQL5) - Signal Generator

### Core Functions
1. **Trade Monitoring**: Real-time monitoring of account positions and orders
2. **Signal Generation**: Create standardized trading signals from account activity
3. **HTTP Communication**: Send signals to backend via REST API
4. **Error Handling**: Robust network failure and retry mechanisms

### Required Inputs
```mql5
// EA Input Parameters
input string    BackendURL = "https://api.copytrading.com";
input string    APIKey = "";
input string    AccountID = "";
input int       SignalDelay = 1000;        // Milliseconds
input bool      EnableFiltering = true;
input double    MinLotSize = 0.01;
input double    MaxLotSize = 100.0;
input string    AllowedSymbols = "EURUSD,GBPUSD,USDJPY";
input int       MaxRetries = 3;
input int       TimeoutMS = 5000;
```

### Signal Structure
```json
{
  "signal_id": "uuid-v4",
  "master_account": "********",
  "timestamp": "2025-06-23T12:00:00.000Z",
  "action": "OPEN|CLOSE|MODIFY",
  "symbol": "EURUSD",
  "order_type": "BUY|SELL|BUY_LIMIT|SELL_LIMIT|BUY_STOP|SELL_STOP",
  "volume": 0.1,
  "price": 1.0850,
  "stop_loss": 1.0800,
  "take_profit": 1.0900,
  "comment": "Signal from EA Master",
  "magic_number": 12345,
  "original_ticket": *********
}
```

### Key Implementation Features
- **Duplicate Prevention**: Track sent signals using unique IDs
- **Network Resilience**: Exponential backoff retry mechanism
- **Memory Management**: Efficient signal queue management
- **Logging**: Comprehensive debug and error logging

### Error Handling Strategy
```mql5
enum SignalResult {
    SIGNAL_SUCCESS,
    SIGNAL_NETWORK_ERROR,
    SIGNAL_AUTH_ERROR,
    SIGNAL_VALIDATION_ERROR,
    SIGNAL_TIMEOUT_ERROR
};
```

## B) EA SLAVE (MQL5) - Signal Executor

### Core Functions
1. **Signal Polling**: Regular HTTP requests to fetch pending signals
2. **Trade Execution**: Execute received signals on local account
3. **Position Management**: Synchronize positions with master account
4. **Risk Management**: Apply local risk controls and lot scaling

### Required Inputs
```mql5
// EA Input Parameters
input string    BackendURL = "https://api.copytrading.com";
input string    APIKey = "";
input string    SlaveAccountID = "";
input string    MasterAccountID = "";
input int       PollingInterval = 2000;    // Milliseconds
input double    LotMultiplier = 1.0;
input double    MaxDrawdown = 20.0;        // Percentage
input double    MaxDailyLoss = 5.0;        // Percentage
input bool      EnableRiskManagement = true;
input int       MaxConcurrentTrades = 10;
input string    TradingHours = "00:00-23:59";
```

### Risk Management Features
```mql5
class RiskManager {
private:
    double m_maxDrawdown;
    double m_maxDailyLoss;
    double m_accountBalance;
    double m_dailyStartBalance;

public:
    bool CheckDrawdownLimit();
    bool CheckDailyLossLimit();
    double CalculateOptimalLotSize(double masterLot);
    bool IsTradeAllowed(string symbol);
};
```

### Synchronization Mechanisms
- **Signal Deduplication**: Track processed signal IDs
- **Position Reconciliation**: Periodic position sync with master
- **Order State Management**: Handle partial fills and modifications

### Trade Execution Flow
```
1. Poll for new signals (every 1-2 seconds)
2. Validate signal against risk parameters
3. Calculate appropriate lot size
4. Execute trade with error handling
5. Send execution confirmation to backend
6. Update local position tracking
```

## C) Backend (NestJS) - Core API Service

### Module Structure
```
src/
├── auth/                   # Authentication & Authorization
│   ├── auth.module.ts
│   ├── auth.service.ts
│   ├── jwt.strategy.ts
│   └── api-key.guard.ts
├── users/                  # User Management
│   ├── users.module.ts
│   ├── users.service.ts
│   ├── users.controller.ts
│   └── dto/
├── accounts/               # MT5 Account Management
│   ├── accounts.module.ts
│   ├── accounts.service.ts
│   ├── accounts.controller.ts
│   └── entities/
├── signals/                # Trading Signal Processing
│   ├── signals.module.ts
│   ├── signals.service.ts
│   ├── signals.controller.ts
│   ├── signals.gateway.ts  # WebSocket
│   └── dto/
├── trading/                # Trade Execution & History
│   ├── trading.module.ts
│   ├── trading.service.ts
│   ├── trading.controller.ts
│   └── entities/
├── admin/                  # Admin Panel APIs
│   ├── admin.module.ts
│   ├── admin.service.ts
│   └── admin.controller.ts
├── common/                 # Shared Components
│   ├── guards/
│   ├── interceptors/
│   ├── pipes/
│   └── decorators/
└── config/                 # Configuration
    ├── database.config.ts
    ├── redis.config.ts
    └── app.config.ts
```

### Core Services Architecture
```typescript
// Signal Processing Service
@Injectable()
export class SignalsService {
  constructor(
    @InjectRepository(TradingSignal)
    private signalsRepository: Repository<TradingSignal>,
    private redisService: RedisService,
    private signalsGateway: SignalsGateway
  ) {}

  async publishSignal(signalDto: CreateSignalDto): Promise<TradingSignal>;
  async getSignalsForAccount(accountId: string): Promise<TradingSignal[]>;
  async markSignalProcessed(signalId: string, accountId: string): Promise<void>;
  async getSignalHistory(filters: SignalHistoryDto): Promise<PaginatedResult>;
}
```

### Real-time Notification System
```typescript
// WebSocket Gateway for Real-time Updates
@WebSocketGateway({
  cors: { origin: '*' },
  namespace: '/signals'
})
export class SignalsGateway {
  @WebSocketServer()
  server: Server;

  // Broadcast new signal to subscribed clients
  broadcastSignal(signal: TradingSignal, targetAccounts: string[]) {
    targetAccounts.forEach(accountId => {
      this.server.to(`account_${accountId}`).emit('new_signal', signal);
    });
  }

  // Handle client subscriptions
  @SubscribeMessage('subscribe_account')
  handleAccountSubscription(client: Socket, accountId: string) {
    client.join(`account_${accountId}`);
  }
}
```

### Caching Strategy
```typescript
// Redis Caching Implementation
@Injectable()
export class CacheService {
  constructor(@Inject('REDIS_CLIENT') private redis: Redis) {}

  // Cache active signals for 5 minutes
  async cacheSignals(accountId: string, signals: TradingSignal[]): Promise<void> {
    await this.redis.setex(
      `signals:${accountId}`,
      300,
      JSON.stringify(signals)
    );
  }

  // Pub/Sub for real-time signal distribution
  async publishSignal(signal: TradingSignal): Promise<void> {
    await this.redis.publish('new_signals', JSON.stringify(signal));
  }
}
```

## D) Frontend Dashboard (NextJS) - Web Interface

### Application Structure
```
src/
├── app/                    # App Router (Next.js 14+)
│   ├── (auth)/
│   │   ├── login/
│   │   └── register/
│   ├── (dashboard)/
│   │   ├── dashboard/
│   │   ├── accounts/
│   │   ├── signals/
│   │   ├── trading/
│   │   └── analytics/
│   ├── (admin)/
│   │   ├── users/
│   │   ├── system/
│   │   └── monitoring/
│   └── api/               # API Routes
├── components/            # Reusable Components
│   ├── ui/               # Base UI Components
│   ├── charts/           # Trading Charts
│   ├── forms/            # Form Components
│   └── layout/           # Layout Components
├── lib/                  # Utilities & Configurations
│   ├── api.ts           # API Client
│   ├── auth.ts          # Authentication
│   ├── websocket.ts     # WebSocket Client
│   └── utils.ts         # Helper Functions
├── stores/               # State Management
│   ├── auth.store.ts
│   ├── signals.store.ts
│   └── accounts.store.ts
└── types/                # TypeScript Definitions
    ├── api.types.ts
    ├── signal.types.ts
    └── user.types.ts
```

### User Pages Specifications

#### 1. Dashboard Overview
```typescript
// Dashboard Component Features
interface DashboardProps {
  user: User;
  accounts: MT5Account[];
  recentSignals: TradingSignal[];
  performance: PerformanceMetrics;
}

// Key Metrics Display
- Total Accounts Connected
- Active Signals Today
- Total P&L (Daily/Weekly/Monthly)
- Success Rate Percentage
- Real-time Account Status
```

#### 2. Account Configuration
```typescript
// Account Management Interface
interface AccountConfigProps {
  accounts: MT5Account[];
  onAddAccount: (account: CreateAccountDto) => void;
  onUpdateAccount: (id: string, updates: UpdateAccountDto) => void;
  onDeleteAccount: (id: string) => void;
}

// Configuration Options
- MT5 Server Details
- Account Credentials (Encrypted)
- Risk Management Settings
- Signal Subscription Preferences
- Trading Hours Configuration
```

#### 3. Trade Monitoring
```typescript
// Real-time Trade Monitoring
interface TradeMonitorProps {
  signals: TradingSignal[];
  executions: TradeExecution[];
  filters: TradeFilters;
}

// Features
- Live Signal Feed
- Execution Status Tracking
- P&L Calculations
- Trade History with Filtering
- Export Functionality (CSV/Excel)
```

#### 4. Performance Analytics
```typescript
// Analytics Dashboard
interface AnalyticsProps {
  timeRange: DateRange;
  accounts: string[];
  metrics: PerformanceMetrics;
}

// Charts & Metrics
- Equity Curve Visualization
- Drawdown Analysis
- Win/Loss Ratio Charts
- Monthly Performance Heatmap
- Risk-Adjusted Returns
```

### Admin Pages Specifications

#### 1. User Management
```typescript
// Admin User Management
interface UserManagementProps {
  users: User[];
  pagination: PaginationInfo;
  filters: UserFilters;
}

// Admin Features
- User Registration Approval
- Account Suspension/Activation
- Subscription Management
- Usage Analytics per User
- Bulk Operations
```

#### 2. System Monitoring
```typescript
// System Health Dashboard
interface SystemMonitoringProps {
  systemMetrics: SystemMetrics;
  alerts: SystemAlert[];
  services: ServiceStatus[];
}

// Monitoring Features
- API Response Times
- Database Performance
- Redis Cache Status
- Signal Processing Metrics
- Error Rate Monitoring
```

### Real-time Updates Implementation

#### WebSocket Client
```typescript
// WebSocket Connection Management
class WebSocketClient {
  private socket: Socket;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;

  constructor(private authToken: string) {
    this.connect();
  }

  private connect() {
    this.socket = io('/signals', {
      auth: { token: this.authToken },
      transports: ['websocket']
    });

    this.socket.on('connect', () => {
      console.log('Connected to signal server');
      this.reconnectAttempts = 0;
    });

    this.socket.on('new_signal', (signal: TradingSignal) => {
      // Update signal store
      useSignalStore.getState().addSignal(signal);
    });

    this.socket.on('signal_executed', (execution: TradeExecution) => {
      // Update execution status
      useSignalStore.getState().updateExecution(execution);
    });
  }

  subscribeToAccount(accountId: string) {
    this.socket.emit('subscribe_account', accountId);
  }
}
```

#### State Management with Zustand
```typescript
// Signal Store
interface SignalStore {
  signals: TradingSignal[];
  executions: TradeExecution[];
  loading: boolean;
  error: string | null;

  // Actions
  addSignal: (signal: TradingSignal) => void;
  updateExecution: (execution: TradeExecution) => void;
  fetchSignalHistory: (filters: SignalFilters) => Promise<void>;
  clearError: () => void;
}

export const useSignalStore = create<SignalStore>((set, get) => ({
  signals: [],
  executions: [],
  loading: false,
  error: null,

  addSignal: (signal) => set((state) => ({
    signals: [signal, ...state.signals].slice(0, 100) // Keep last 100
  })),

  updateExecution: (execution) => set((state) => ({
    executions: state.executions.map(ex =>
      ex.signal_id === execution.signal_id ? execution : ex
    )
  })),

  fetchSignalHistory: async (filters) => {
    set({ loading: true, error: null });
    try {
      const response = await api.getSignalHistory(filters);
      set({ signals: response.data, loading: false });
    } catch (error) {
      set({ error: error.message, loading: false });
    }
  },

  clearError: () => set({ error: null })
}));
```

### UI Component Library

#### Base Components (shadcn/ui + Custom)
```typescript
// Trading-specific Components
- SignalCard: Display individual trading signals
- AccountStatusBadge: Show account connection status
- ProfitLossIndicator: Color-coded P&L display
- TradingChart: Candlestick charts with signal markers
- RiskMeter: Visual risk level indicator
- ExecutionStatusIcon: Trade execution status
```

#### Responsive Design
```css
/* Mobile-first responsive design */
- Mobile: 320px - 768px (Stack layout)
- Tablet: 768px - 1024px (2-column layout)
- Desktop: 1024px+ (3-column layout)
- Large Desktop: 1440px+ (4-column layout)
```

### Performance Optimizations

#### Code Splitting & Lazy Loading
```typescript
// Route-based code splitting
const Dashboard = lazy(() => import('./pages/Dashboard'));
const Analytics = lazy(() => import('./pages/Analytics'));
const AdminPanel = lazy(() => import('./pages/AdminPanel'));

// Component-based lazy loading
const TradingChart = lazy(() => import('./components/TradingChart'));
```

#### Data Fetching Strategy
```typescript
// Server-side rendering for initial data
export async function generateStaticProps() {
  const initialData = await fetchDashboardData();
  return { props: { initialData } };
}

// Client-side updates via SWR
const { data, error, mutate } = useSWR(
  '/api/signals/recent',
  fetcher,
  { refreshInterval: 2000 } // 2-second refresh
);
```