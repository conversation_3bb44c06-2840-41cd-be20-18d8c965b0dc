import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  Index,
} from 'typeorm';
import { TradingSignal } from '../../signals/entities/trading-signal.entity';
import { MT5Account } from '../../accounts/entities/mt5-account.entity';

export enum ExecutionStatus {
  PENDING = 'pending',
  EXECUTED = 'executed',
  FAILED = 'failed',
  REJECTED = 'rejected',
  PARTIAL = 'partial',
}

@Entity('trade_executions')
@Index(['signal_id'])
@Index(['slave_account_id'])
@Index(['execution_status'])
@Index(['execution_time'])
@Index(['mt5_ticket'])
export class TradeExecution {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  signal_id: string;

  @Column({ type: 'uuid' })
  slave_account_id: string;

  @Column({
    type: 'enum',
    enum: ExecutionStatus,
    default: ExecutionStatus.PENDING,
  })
  execution_status: ExecutionStatus;

  @Column({ type: 'bigint', nullable: true })
  mt5_ticket: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  executed_volume: number;

  @Column({ type: 'decimal', precision: 10, scale: 5, nullable: true })
  executed_price: number;

  @Column({ type: 'timestamptz', nullable: true })
  execution_time: Date;

  @Column({ type: 'int', nullable: true })
  error_code: number;

  @Column({ type: 'text', nullable: true })
  error_message: string;

  @Column({ type: 'decimal', precision: 10, scale: 5, nullable: true })
  slippage: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  commission: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  swap: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  profit: number;

  @Column({ type: 'jsonb', nullable: true })
  execution_data: Record<string, any>;

  @CreateDateColumn({ type: 'timestamptz' })
  created_at: Date;

  @UpdateDateColumn({ type: 'timestamptz' })
  updated_at: Date;

  // Relations
  @ManyToOne(() => TradingSignal, (signal) => signal.executions)
  signal: TradingSignal;

  @ManyToOne(() => MT5Account, (account) => account.trade_executions)
  slave_account: MT5Account;

  // Virtual properties
  get is_successful(): boolean {
    return this.execution_status === ExecutionStatus.EXECUTED;
  }

  get is_failed(): boolean {
    return [ExecutionStatus.FAILED, ExecutionStatus.REJECTED].includes(this.execution_status);
  }

  get execution_latency(): number {
    if (!this.execution_time || !this.created_at) return 0;
    return this.execution_time.getTime() - this.created_at.getTime();
  }

  get net_profit(): number {
    const profit = this.profit || 0;
    const commission = this.commission || 0;
    const swap = this.swap || 0;
    return profit - commission - swap;
  }

  get slippage_pips(): number {
    if (!this.slippage) return 0;
    // Convert slippage to pips (assuming 4-digit quotes for major pairs)
    return Math.abs(this.slippage * 10000);
  }
}