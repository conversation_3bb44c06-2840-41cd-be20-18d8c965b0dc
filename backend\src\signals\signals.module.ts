import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SignalsService } from './signals.service';
import { SignalsController } from './signals.controller';
import { TradingSignal } from './entities/trading-signal.entity';
import { SignalSubscription } from './entities/signal-subscription.entity';

@Module({
  imports: [TypeOrmModule.forFeature([TradingSignal, SignalSubscription])],
  controllers: [SignalsController],
  providers: [SignalsService],
  exports: [SignalsService],
})
export class SignalsModule {}