import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { TypeOrmModuleOptions, TypeOrmOptionsFactory } from '@nestjs/typeorm';
import { User } from '../users/entities/user.entity';
import { MT5Account } from '../accounts/entities/mt5-account.entity';
import { TradingSignal } from '../signals/entities/trading-signal.entity';
import { SignalSubscription } from '../signals/entities/signal-subscription.entity';
import { TradeExecution } from '../trading/entities/trade-execution.entity';
import { AccountPerformance } from '../accounts/entities/account-performance.entity';

@Injectable()
export class DatabaseConfig implements TypeOrmOptionsFactory {
  constructor(private configService: ConfigService) {}

  createTypeOrmOptions(): TypeOrmModuleOptions {
    return {
      type: 'postgres',
      host: this.configService.get('DB_HOST', 'localhost'),
      port: this.configService.get('DB_PORT', 5432),
      username: this.configService.get('DB_USERNAME', 'copytrading'),
      password: this.configService.get('DB_PASSWORD'),
      database: this.configService.get('DB_NAME', 'mt5_copytrading'),

      // Entities
      entities: [
        User,
        MT5Account,
        TradingSignal,
        SignalSubscription,
        TradeExecution,
        AccountPerformance,
      ],

      // Development settings
      synchronize: this.configService.get('DB_SYNCHRONIZE', false),
      logging: this.configService.get('DB_LOGGING', false),

      // Connection pooling for high performance
      extra: {
        max: 20,                    // Maximum connections
        min: 5,                     // Minimum connections
        acquire: 30000,             // Maximum time to get connection (ms)
        idle: 10000,                // Maximum idle time (ms)
        evict: 1000,                // Eviction run interval (ms)
        handleDisconnects: true,    // Reconnect on disconnect

        // Connection validation
        testOnBorrow: true,
        validationQuery: 'SELECT 1',

        // SSL configuration for production
        ssl: this.configService.get('NODE_ENV') === 'production' ? {
          rejectUnauthorized: false
        } : false
      },

      // Migration settings
      migrationsRun: false,
      migrations: ['dist/migrations/*.js'],
      migrationsTableName: 'migrations',

      // Retry connection
      retryAttempts: 3,
      retryDelay: 3000,
    };
  }
}