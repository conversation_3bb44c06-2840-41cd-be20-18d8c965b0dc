{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2021", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "paths": {"@/*": ["src/*"], "@/auth/*": ["src/auth/*"], "@/users/*": ["src/users/*"], "@/accounts/*": ["src/accounts/*"], "@/signals/*": ["src/signals/*"], "@/trading/*": ["src/trading/*"], "@/admin/*": ["src/admin/*"], "@/common/*": ["src/common/*"], "@/config/*": ["src/config/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "test"]}